# 高级录屏程序 v1.0

一个功能强大的Python录屏程序，专门设计用于绕过部分软件的录屏检测，解决播放器检测到录屏后视频黑屏的问题。

## 🌟 主要特性

- **多种截屏方法**: 支持MSS、GDI、BitBlt、DirectX等多种截屏技术
- **反检测机制**: 内置多种反检测技术，绕过常见的录屏检测
- **进程隐藏**: 支持进程名伪装和窗口隐藏
- **自适应模式**: 根据检测到的软件自动调整隐身策略
- **用户友好界面**: 提供直观的GUI界面和命令行界面
- **配置管理**: 支持配置文件的保存、加载和导入导出
- **实时监控**: 实时显示录制状态和检测信息

## 📋 系统要求

- Windows 10/11 (64位)
- Python 3.8 或更高版本
- 至少 4GB RAM
- 500MB 可用磁盘空间

## 🚀 快速安装

### 方法一：使用pip安装依赖

```bash
# 克隆或下载项目文件
# 进入项目目录
cd 贪吃蛇

# 安装依赖
pip install -r requirements.txt
```

### 方法二：手动安装依赖

```bash
pip install opencv-python
pip install numpy
pip install pyautogui
pip install pillow
pip install mss
pip install psutil
pip install pywin32
```

## 📦 依赖包说明

| 包名 | 版本要求 | 用途 |
|------|----------|------|
| opencv-python | >=4.5.0 | 视频处理和编码 |
| numpy | >=1.20.0 | 数组处理 |
| pyautogui | >=0.9.50 | 屏幕截图 |
| pillow | >=8.0.0 | 图像处理 |
| mss | >=6.0.0 | 高性能截屏 |
| psutil | >=5.8.0 | 进程管理 |
| pywin32 | >=227 | Windows API调用 |

## 🎯 使用方法

### GUI界面模式（推荐）

```bash
python gui_recorder.py
```

启动后会显示图形界面，包含以下功能：
- 录制控制（开始/停止）
- 文件设置（输出路径、录制时长）
- 录制参数（截屏方法、帧率、质量）
- 反检测设置（隐身模式、窗口隐藏等）
- 实时状态监控

### 命令行模式

```bash
python screen_recorder.py
```

支持的命令：
- `start [文件名] [时长]` - 开始录制
- `stop` - 停止录制
- `status` - 查看状态
- `method [方法名]` - 设置截屏方法
- `fps [数值]` - 设置帧率
- `quit` - 退出程序

### 配置文件模式

```bash
python config_manager.py
```

管理和测试配置文件功能。

## ⚙️ 配置说明

### 录制配置
- **截屏方法**: mss（推荐）、gdi、bitblt、directx
- **帧率**: 1-60 FPS，默认30
- **质量**: 1-100，默认80
- **输出格式**: mp4、avi等

### 反检测配置
- **隐身模式**: 启用多种反检测技术
- **窗口隐藏**: 隐藏程序窗口
- **进程伪装**: 伪装成系统进程
- **API Hook**: Hook常见检测API
- **自适应模式**: 根据环境自动调整

## 🛡️ 反检测技术

### 1. 进程隐藏
- 修改进程名称和标题
- 创建假的系统进程
- 隐藏相关窗口

### 2. API Hook
- Hook GetWindowText API
- Hook FindWindow API
- Hook EnumWindows API
- Hook截屏相关API

### 3. 内存混淆
- 修改内存特征字符串
- 添加随机内存分配
- 改变内存布局

### 4. 时序随机化
- 添加随机延迟
- 避免时序检测
- 模拟正常用户行为

### 5. 自适应策略
- 检测反录屏软件
- 根据环境调整策略
- 动态切换截屏方法

## 📁 文件结构

```
贪吃蛇/
├── screen_recorder.py      # 主录屏程序
├── gui_recorder.py         # GUI界面
├── anti_detection.py       # 反检测模块
├── config_manager.py       # 配置管理器
├── requirements.txt        # 依赖列表
├── README.md              # 说明文档
└── recordings/            # 录制文件目录（自动创建）
```

## 🔧 高级用法

### 自定义配置文件

创建 `recorder_config.json` 文件：

```json
{
  "recording": {
    "method": "mss",
    "fps": 30,
    "quality": 80,
    "output_format": "mp4",
    "output_dir": "./recordings"
  },
  "anti_detection": {
    "stealth_mode": true,
    "hide_window": true,
    "fake_process": false,
    "adaptive_mode": true
  }
}
```

### 批量录制脚本

```python
from screen_recorder import AntiDetectionRecorder
import time

recorder = AntiDetectionRecorder()

# 录制多个片段
for i in range(3):
    filename = f"recording_{i+1}.mp4"
    recorder.start_recording(filename, 60)  # 录制60秒
    time.sleep(65)  # 等待录制完成
```

## 🚨 注意事项

### 安全提醒
- 本程序仅供学习和合法用途使用
- 请遵守当地法律法规和软件使用协议
- 不要用于非法录制受版权保护的内容

### 使用建议
- 首次使用建议先测试各种截屏方法
- 在录制重要内容前先进行短时间测试
- 定期备份录制文件和配置
- 关闭不必要的安全软件以避免误报

### 兼容性
- 某些杀毒软件可能会误报，请添加白名单
- 部分反检测功能需要管理员权限
- 在虚拟机中可能效果有限

## 🐛 故障排除

### 常见问题

**Q: 程序无法启动**
A: 检查Python版本和依赖包是否正确安装

**Q: 录制的视频是黑屏**
A: 尝试切换不同的截屏方法，或启用更多反检测功能

**Q: 录制文件很大**
A: 降低帧率或质量设置，或使用更高效的编码格式

**Q: 被杀毒软件拦截**
A: 将程序目录添加到杀毒软件白名单

### 调试模式

启用调试模式获取更多信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误信息截图
- 使用的配置参数

## 📄 许可证

本项目仅供学习和研究使用。使用者需要自行承担使用风险，并遵守相关法律法规。

## 🔄 更新日志

### v1.0 (2024-01-01)
- 初始版本发布
- 支持多种截屏方法
- 实现基础反检测功能
- 提供GUI和命令行界面
- 添加配置管理系统

---

**免责声明**: 本软件仅供学习和研究目的使用。使用者应当遵守当地法律法规，不得将本软件用于任何非法用途。开发者不承担因使用本软件而产生的任何法律责任。
