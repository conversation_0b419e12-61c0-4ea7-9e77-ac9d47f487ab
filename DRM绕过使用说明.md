# DRM绕过录屏功能使用说明

## 概述

本录屏程序现已集成强大的DRM（数字版权管理）绕过功能，专门用于解决视频播放器的反录屏保护问题。当某些视频播放器检测到录屏软件时会显示黑屏，我们的DRM绕过技术可以有效解决这个问题。

## 功能特点

### 🔧 多种绕过技术
- **DXGI Desktop Duplication API** - 最难被检测的底层捕获方法
- **镜像驱动捕获** - 绕过硬件级保护
- **硬件覆盖层捕获** - 处理硬件加速视频
- **内存注入捕获** - 直接读取显存数据
- **窗口合成捕获** - 逐窗口捕获并智能合成
- **PrintScreen钩子** - 模拟系统截屏
- **增强GDI+捕获** - 多模式GDI捕获

### 🛡️ 智能检测与适应
- 自动检测最有效的绕过方法
- 动态切换捕获技术
- 实时适应不同的DRM保护机制

## 使用方法

### 1. 启用DRM绕过功能

#### GUI界面方式：
1. 打开录屏程序GUI界面
2. 在"录制设置"区域找到"启用DRM绕过"选项
3. 勾选该选项即可启用DRM绕过功能

#### 命令行方式：
```python
from screen_recorder import AntiDetectionRecorder

recorder = AntiDetectionRecorder()
recorder.enable_drm_bypass(True)  # 启用DRM绕过
recorder.start_recording("output.avi")
```

### 2. 针对不同播放器的建议

#### 常见视频播放器：
- **Netflix、Disney+等流媒体** - 使用DXGI或内存注入模式
- **本地视频播放器** - 使用窗口合成或硬件覆盖模式
- **浏览器视频** - 使用PrintScreen钩子或增强GDI模式

#### 设置建议：
```python
# 对于强DRM保护的流媒体
recorder.set_method('drm_bypass')
recorder.enable_drm_bypass(True)

# 对于一般视频播放器
recorder.enable_drm_bypass(True)  # 保持默认方法，启用DRM绕过作为备选
```

## 技术原理

### DRM保护机制
视频播放器的DRM保护通常通过以下方式工作：
1. **API钩子检测** - 监控常见的截屏API调用
2. **进程检测** - 扫描运行中的录屏软件进程
3. **硬件保护** - 使用硬件覆盖层隐藏视频内容
4. **内存保护** - 加密视频帧数据

### 我们的绕过技术

#### 1. DXGI Desktop Duplication
```
优势：底层API，难以被检测
原理：直接从显卡获取桌面图像
适用：Windows 8+系统
```

#### 2. 内存注入捕获
```
优势：绕过大部分软件检测
原理：直接读取显存中的像素数据
要求：管理员权限
```

#### 3. 窗口合成技术
```
优势：逐窗口捕获，精确度高
原理：使用PrintWindow API捕获每个窗口
特点：可以捕获被遮挡的窗口内容
```

## 注意事项

### ⚠️ 重要提醒
1. **管理员权限** - 某些高级绕过技术需要管理员权限
2. **系统兼容性** - 部分功能需要Windows 8或更高版本
3. **性能影响** - DRM绕过可能会增加CPU使用率
4. **法律合规** - 请确保录制内容符合相关法律法规

### 🔧 故障排除

#### 问题：仍然出现黑屏
**解决方案：**
1. 确保以管理员身份运行程序
2. 尝试不同的DRM绕过方法
3. 检查是否有其他安全软件干扰

#### 问题：录制性能下降
**解决方案：**
1. 降低录制帧率（推荐15-24fps）
2. 调整录制质量设置
3. 关闭不必要的后台程序

#### 问题：某些播放器仍无法录制
**解决方案：**
1. 更新显卡驱动程序
2. 尝试在兼容模式下运行播放器
3. 使用窗口模式而非全屏模式播放

## 高级配置

### 自定义DRM绕过策略
```python
from drm_bypass import DRMBypassCapture

# 创建自定义DRM绕过器
drm_capture = DRMBypassCapture()

# 手动指定绕过方法
frame = drm_capture._dxgi_desktop_duplication()  # DXGI方法
frame = drm_capture._memory_injection_capture()  # 内存注入
frame = drm_capture._window_composition_capture()  # 窗口合成
```

### 性能优化设置
```python
# 优化设置示例
recorder = AntiDetectionRecorder()
recorder.set_fps(24)  # 降低帧率
recorder.set_quality(70)  # 适中质量
recorder.enable_drm_bypass(True)
```

## 更新日志

### v1.0 (当前版本)
- ✅ 集成7种DRM绕过技术
- ✅ 智能方法选择和切换
- ✅ GUI界面集成
- ✅ 性能优化

### 计划功能
- 🔄 更多DRM绕过算法
- 🔄 自动播放器识别
- 🔄 云端DRM数据库
- 🔄 实时效果预览

## 技术支持

如果您在使用DRM绕过功能时遇到问题，请：

1. 检查系统要求和权限设置
2. 查看程序日志文件
3. 尝试不同的绕过方法
4. 确保播放器和系统版本兼容

---

**免责声明：** 本功能仅供学习和研究使用，请确保您的录制行为符合相关法律法规和服务条款。
