#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反检测模块 - 专门用于绕过录屏检测
包含多种反检测技术和策略
"""

import ctypes
from ctypes import wintypes, windll
import win32api
import win32con
import win32gui
import win32process
import win32security
import win32service
import win32serviceutil
import psutil
import os
import sys
import threading
import time
import random
import string
import subprocess
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class AntiDetectionManager:
    """反检测管理器"""
    
    def __init__(self):
        self.active_hooks = []
        self.original_functions = {}
        self.stealth_processes = []
        self.fake_processes = []
        
    def enable_all_protections(self):
        """启用所有保护机制（不包括会打开程序的功能）"""
        try:
            self.hide_from_process_list()
            self.spoof_window_detection()
            self.hook_screenshot_apis()
            # 禁用会创建进程的功能
            # self.create_decoy_processes()
            self.modify_memory_signatures()
            self.randomize_timing()
            logger.info("反检测保护已启用（不包括进程创建）")
        except Exception as e:
            logger.error(f"启用保护机制失败: {e}")
    
    def hide_from_process_list(self):
        """从进程列表中隐藏"""
        try:
            # 方法1: 修改进程名
            self._change_process_name()
            
            # 方法2: 创建假进程
            self._create_fake_processes()
            
            # 方法3: 隐藏窗口
            self._hide_all_windows()
            
        except Exception as e:
            logger.error(f"进程隐藏失败: {e}")
    
    def _change_process_name(self):
        """修改进程名称"""
        try:
            # 生成随机的系统进程名
            system_processes = [
                "svchost.exe", "explorer.exe", "winlogon.exe", 
                "csrss.exe", "dwm.exe", "audiodg.exe"
            ]
            fake_name = random.choice(system_processes)
            
            # 设置控制台标题
            ctypes.windll.kernel32.SetConsoleTitleW(fake_name)
            
            # 尝试修改进程映像名（需要特殊权限）
            try:
                import win32process
                handle = win32api.GetCurrentProcess()
                # 这里可以添加更复杂的进程名修改逻辑
            except:
                pass
                
        except Exception as e:
            logger.warning(f"进程名修改失败: {e}")
    
    def _create_fake_processes(self):
        """创建假的系统进程（已禁用，避免打开不必要的程序）"""
        try:
            # 注释掉会打开程序的代码，改为仅在内存中模拟
            logger.info("假进程创建已禁用，避免打开不必要的程序")
            # fake_processes = ["notepad.exe", "calc.exe", "mspaint.exe"]
            # for process in fake_processes:
            #     try:
            #         # 启动假进程但立即最小化
            #         subprocess.Popen([process],
            #                        creationflags=subprocess.CREATE_NO_WINDOW)
            #         time.sleep(0.1)
            #     except:
            #         continue
        except Exception as e:
            logger.warning(f"创建假进程失败: {e}")
    
    def _hide_all_windows(self):
        """隐藏所有相关窗口（已禁用，避免隐藏GUI界面）"""
        try:
            # 只隐藏控制台窗口，不隐藏GUI界面
            self._hide_console_only()

            # 禁用隐藏其他窗口的功能，避免隐藏GUI界面
            # def enum_windows_callback(hwnd, windows):
            #     if win32gui.IsWindowVisible(hwnd):
            #         window_text = win32gui.GetWindowText(hwnd)
            #         if "python" in window_text.lower() or "录屏" in window_text:
            #             win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
            #     return True
            #
            # win32gui.EnumWindows(enum_windows_callback, [])

        except Exception as e:
            logger.warning(f"窗口隐藏失败: {e}")

    def _hide_console_only(self):
        """只隐藏控制台窗口，不影响GUI界面"""
        try:
            # 隐藏控制台窗口
            console_window = ctypes.windll.kernel32.GetConsoleWindow()
            if console_window:
                ctypes.windll.user32.ShowWindow(console_window, 0)  # SW_HIDE
                logger.info("控制台窗口已隐藏")
        except Exception as e:
            logger.warning(f"隐藏控制台窗口失败: {e}")
    
    def spoof_window_detection(self):
        """欺骗窗口检测"""
        try:
            # Hook GetWindowText API
            self._hook_get_window_text()
            
            # Hook FindWindow API
            self._hook_find_window()
            
            # Hook EnumWindows API
            self._hook_enum_windows()
            
        except Exception as e:
            logger.error(f"窗口检测欺骗失败: {e}")
    
    def _hook_get_window_text(self):
        """Hook GetWindowText API"""
        try:
            # 这里需要实现API Hook
            # 由于复杂性，这里只是示例框架
            pass
        except Exception as e:
            logger.warning(f"GetWindowText Hook失败: {e}")
    
    def _hook_find_window(self):
        """Hook FindWindow API"""
        try:
            # Hook FindWindow API以返回假信息
            pass
        except Exception as e:
            logger.warning(f"FindWindow Hook失败: {e}")
    
    def _hook_enum_windows(self):
        """Hook EnumWindows API"""
        try:
            # Hook EnumWindows以过滤敏感窗口
            pass
        except Exception as e:
            logger.warning(f"EnumWindows Hook失败: {e}")
    
    def hook_screenshot_apis(self):
        """Hook截屏相关API"""
        try:
            # Hook BitBlt
            self._hook_bitblt()
            
            # Hook GetDC/GetWindowDC
            self._hook_get_dc()
            
            # Hook PrintWindow
            self._hook_print_window()
            
        except Exception as e:
            logger.error(f"截屏API Hook失败: {e}")
    
    def _hook_bitblt(self):
        """Hook BitBlt API"""
        try:
            # 实现BitBlt Hook以检测截屏行为
            pass
        except Exception as e:
            logger.warning(f"BitBlt Hook失败: {e}")
    
    def _hook_get_dc(self):
        """Hook GetDC相关API"""
        try:
            # Hook GetDC和GetWindowDC
            pass
        except Exception as e:
            logger.warning(f"GetDC Hook失败: {e}")
    
    def _hook_print_window(self):
        """Hook PrintWindow API"""
        try:
            # Hook PrintWindow API
            pass
        except Exception as e:
            logger.warning(f"PrintWindow Hook失败: {e}")
    
    def create_decoy_processes(self):
        """创建诱饵进程（已禁用，避免创建不必要的后台进程）"""
        try:
            # 禁用诱饵进程创建，避免占用系统资源
            logger.info("诱饵进程创建已禁用，避免创建不必要的后台进程")
            # 创建多个看起来正常的进程
            # decoy_commands = [
            #     ["ping", "127.0.0.1", "-t"],
            #     ["timeout", "3600"],
            #     ["powershell", "-Command", "Start-Sleep 3600"]
            # ]
            #
            # for cmd in decoy_commands:
            #     try:
            #         process = subprocess.Popen(cmd,
            #                                  creationflags=subprocess.CREATE_NO_WINDOW,
            #                                  stdout=subprocess.DEVNULL,
            #                                  stderr=subprocess.DEVNULL)
            #         self.fake_processes.append(process)
            #     except:
            #         continue

        except Exception as e:
            logger.warning(f"创建诱饵进程失败: {e}")
    
    def modify_memory_signatures(self):
        """修改内存特征"""
        try:
            # 修改进程内存中的特征字符串
            self._obfuscate_strings()
            
            # 添加随机内存分配
            self._add_random_memory()
            
        except Exception as e:
            logger.warning(f"内存特征修改失败: {e}")
    
    def _obfuscate_strings(self):
        """混淆字符串"""
        try:
            # 创建一些假的字符串来混淆检测
            fake_strings = [
                "Microsoft Windows",
                "System Process",
                "Windows Explorer",
                "Audio Service"
            ]
            
            # 将这些字符串存储在内存中
            self.decoy_strings = fake_strings
            
        except Exception as e:
            logger.warning(f"字符串混淆失败: {e}")
    
    def _add_random_memory(self):
        """添加随机内存分配"""
        try:
            # 分配一些随机内存来改变内存布局
            random_data = []
            for _ in range(10):
                size = random.randint(1024, 8192)
                data = os.urandom(size)
                random_data.append(data)
            
            self.random_memory = random_data
            
        except Exception as e:
            logger.warning(f"随机内存分配失败: {e}")
    
    def randomize_timing(self):
        """随机化时序"""
        try:
            # 添加随机延迟来避免时序检测
            def random_delay():
                while True:
                    delay = random.uniform(0.1, 2.0)
                    time.sleep(delay)
            
            # 在后台线程中运行随机延迟
            delay_thread = threading.Thread(target=random_delay, daemon=True)
            delay_thread.start()
            
        except Exception as e:
            logger.warning(f"时序随机化失败: {e}")
    
    def detect_anti_recording_software(self):
        """检测反录屏软件"""
        try:
            suspicious_processes = [
                "obs", "bandicam", "fraps", "camtasia", "screenrec",
                "dxtory", "action", "mirillis", "nvidia", "amd"
            ]
            
            current_processes = [p.name().lower() for p in psutil.process_iter()]
            
            detected = []
            for proc in current_processes:
                for suspicious in suspicious_processes:
                    if suspicious in proc:
                        detected.append(proc)
            
            if detected:
                logger.warning(f"检测到可能的反录屏软件: {detected}")
                return detected
            
            return []
            
        except Exception as e:
            logger.error(f"检测反录屏软件失败: {e}")
            return []
    
    def adaptive_stealth_mode(self):
        """自适应隐身模式"""
        try:
            # 根据检测到的软件调整策略
            detected_software = self.detect_anti_recording_software()
            
            if detected_software:
                # 如果检测到反录屏软件，增强隐身
                self._enhance_stealth()
            else:
                # 否则使用标准隐身
                self._standard_stealth()
                
        except Exception as e:
            logger.error(f"自适应隐身模式失败: {e}")
    
    def _enhance_stealth(self):
        """增强隐身模式（不创建假进程）"""
        try:
            # 更激进的隐身策略（但不创建进程）
            # self.create_decoy_processes()  # 禁用进程创建
            self.modify_memory_signatures()
            self.randomize_timing()
            self.hook_screenshot_apis()

            logger.info("启用增强隐身模式（无进程创建）")

        except Exception as e:
            logger.warning(f"增强隐身模式失败: {e}")
    
    def _standard_stealth(self):
        """标准隐身模式"""
        try:
            # 标准隐身策略
            self.hide_from_process_list()
            self.spoof_window_detection()
            
            logger.info("启用标准隐身模式")
            
        except Exception as e:
            logger.warning(f"标准隐身模式失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 终止假进程
            for process in self.fake_processes:
                try:
                    process.terminate()
                except:
                    pass
            
            # 清理Hook
            for hook in self.active_hooks:
                try:
                    # 恢复原始函数
                    pass
                except:
                    pass
            
            logger.info("反检测资源已清理")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()


class AdvancedAntiDetection:
    """高级反检测技术"""
    
    @staticmethod
    def inject_fake_screenshot_data():
        """注入假的截屏数据"""
        try:
            # 这里可以实现更复杂的数据注入
            pass
        except Exception as e:
            logger.warning(f"假数据注入失败: {e}")
    
    @staticmethod
    def spoof_graphics_driver():
        """欺骗图形驱动"""
        try:
            # 修改图形驱动相关的检测点
            pass
        except Exception as e:
            logger.warning(f"图形驱动欺骗失败: {e}")
    
    @staticmethod
    def bypass_hardware_detection():
        """绕过硬件检测"""
        try:
            # 绕过基于硬件的检测
            pass
        except Exception as e:
            logger.warning(f"硬件检测绕过失败: {e}")


if __name__ == "__main__":
    # 测试反检测功能
    manager = AntiDetectionManager()
    
    print("反检测模块测试")
    print("启用所有保护...")
    
    manager.enable_all_protections()
    
    print("检测反录屏软件...")
    detected = manager.detect_anti_recording_software()
    if detected:
        print(f"发现: {detected}")
    else:
        print("未发现反录屏软件")
    
    print("启用自适应隐身模式...")
    manager.adaptive_stealth_mode()
    
    print("测试完成")
