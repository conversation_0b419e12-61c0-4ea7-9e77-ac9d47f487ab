#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录屏程序启动器
自动检查依赖并启动程序
"""

import sys
import os
import subprocess
import importlib
import tkinter as tk
from tkinter import messagebox

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'cv2',
        'numpy', 
        'pyautogui',
        'PIL',
        'mss',
        'psutil',
        'win32api'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    try:
        print("正在安装依赖包...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['recordings', 'logs', 'config']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            print(f"创建目录 {directory} 失败: {e}")

def show_startup_dialog():
    """显示启动对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    choice = messagebox.askyesnocancel(
        "录屏程序启动器",
        "选择启动模式:\n\n"
        "是(Yes) - 启动GUI界面\n"
        "否(No) - 启动命令行模式\n"
        "取消(Cancel) - 退出程序"
    )
    
    root.destroy()
    return choice

def start_gui_mode():
    """启动GUI模式"""
    try:
        print("启动GUI模式...")
        subprocess.Popen([sys.executable, 'gui_recorder.py'])
        return True
    except Exception as e:
        print(f"启动GUI模式失败: {e}")
        return False

def start_console_mode():
    """启动命令行模式"""
    try:
        print("启动命令行模式...")
        subprocess.call([sys.executable, 'screen_recorder.py'])
        return True
    except Exception as e:
        print(f"启动命令行模式失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("高级录屏程序启动器 v1.0")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"缺少依赖包: {', '.join(missing)}")
        
        choice = input("是否自动安装依赖包? (y/n): ").lower()
        if choice == 'y':
            if not install_dependencies():
                input("按回车键退出...")
                return
            
            # 重新检查依赖
            missing = check_dependencies()
            if missing:
                print(f"仍然缺少依赖包: {', '.join(missing)}")
                print("请手动安装缺失的依赖包")
                input("按回车键退出...")
                return
        else:
            print("请先安装依赖包:")
            print("pip install -r requirements.txt")
            input("按回车键退出...")
            return
    
    # 创建必要目录
    create_directories()
    
    # 显示启动选择
    try:
        choice = show_startup_dialog()
        
        if choice is True:  # GUI模式
            start_gui_mode()
        elif choice is False:  # 命令行模式
            start_console_mode()
        else:  # 取消
            print("程序已取消")
            
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n手动启动方法:")
        print("GUI模式: python gui_recorder.py")
        print("命令行模式: python screen_recorder.py")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
