#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 管理录屏程序的各种配置
支持配置文件的读取、保存和验证
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import configparser

logger = logging.getLogger(__name__)

@dataclass
class RecordingConfig:
    """录制配置"""
    method: str = "mss"  # 截屏方法
    fps: int = 30  # 帧率
    quality: int = 80  # 质量
    output_format: str = "mp4"  # 输出格式
    output_dir: str = "./recordings"  # 输出目录
    filename_template: str = "screen_record_{timestamp}"  # 文件名模板
    auto_start: bool = False  # 自动开始
    max_duration: int = 3600  # 最大录制时长(秒)
    
@dataclass
class AntiDetectionConfig:
    """反检测配置"""
    stealth_mode: bool = True  # 隐身模式
    hide_window: bool = True  # 隐藏窗口
    fake_process: bool = False  # 创建假进程
    process_disguise: str = "explorer.exe"  # 进程伪装名
    api_hook: bool = False  # API Hook
    memory_obfuscation: bool = False  # 内存混淆
    timing_randomization: bool = False  # 时序随机化
    adaptive_mode: bool = True  # 自适应模式
    
@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "default"  # 主题
    window_size: str = "600x500"  # 窗口大小
    always_on_top: bool = False  # 总是置顶
    minimize_to_tray: bool = True  # 最小化到托盘
    show_notifications: bool = True  # 显示通知
    auto_save_settings: bool = True  # 自动保存设置
    
@dataclass
class AdvancedConfig:
    """高级配置"""
    buffer_size: int = 1024 * 1024  # 缓冲区大小
    thread_count: int = 4  # 线程数
    compression_level: int = 6  # 压缩级别
    hardware_acceleration: bool = True  # 硬件加速
    debug_mode: bool = False  # 调试模式
    log_level: str = "INFO"  # 日志级别
    backup_count: int = 5  # 备份数量

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="recorder_config.json"):
        self.config_file = config_file
        self.recording = RecordingConfig()
        self.anti_detection = AntiDetectionConfig()
        self.ui = UIConfig()
        self.advanced = AdvancedConfig()
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 更新配置对象
                if 'recording' in data:
                    self._update_dataclass(self.recording, data['recording'])
                
                if 'anti_detection' in data:
                    self._update_dataclass(self.anti_detection, data['anti_detection'])
                
                if 'ui' in data:
                    self._update_dataclass(self.ui, data['ui'])
                
                if 'advanced' in data:
                    self._update_dataclass(self.advanced, data['advanced'])
                
                logger.info(f"配置已从 {self.config_file} 加载")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 创建默认配置文件
                
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            logger.info("使用默认配置")
    
    def save_config(self):
        """保存配置文件"""
        try:
            config_data = {
                'recording': asdict(self.recording),
                'anti_detection': asdict(self.anti_detection),
                'ui': asdict(self.ui),
                'advanced': asdict(self.advanced)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return False
    
    def _update_dataclass(self, obj, data):
        """更新数据类对象"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def validate_config(self):
        """验证配置"""
        errors = []
        
        # 验证录制配置
        if self.recording.fps < 1 or self.recording.fps > 60:
            errors.append("帧率必须在1-60之间")
        
        if self.recording.quality < 1 or self.recording.quality > 100:
            errors.append("质量必须在1-100之间")
        
        if self.recording.method not in ['mss', 'gdi', 'bitblt', 'directx']:
            errors.append("不支持的截屏方法")
        
        # 验证输出目录
        try:
            os.makedirs(self.recording.output_dir, exist_ok=True)
        except Exception as e:
            errors.append(f"无法创建输出目录: {e}")
        
        # 验证高级配置
        if self.advanced.buffer_size < 1024:
            errors.append("缓冲区大小不能小于1024字节")
        
        if self.advanced.thread_count < 1 or self.advanced.thread_count > 16:
            errors.append("线程数必须在1-16之间")
        
        return errors
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.recording = RecordingConfig()
        self.anti_detection = AntiDetectionConfig()
        self.ui = UIConfig()
        self.advanced = AdvancedConfig()
        
        logger.info("配置已重置为默认值")
    
    def export_config(self, filename):
        """导出配置到文件"""
        try:
            config_data = {
                'recording': asdict(self.recording),
                'anti_detection': asdict(self.anti_detection),
                'ui': asdict(self.ui),
                'advanced': asdict(self.advanced),
                'version': '1.0',
                'exported_at': str(datetime.now())
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已导出到 {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, filename):
        """从文件导入配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证版本兼容性
            if 'version' in data and data['version'] != '1.0':
                logger.warning(f"配置文件版本不匹配: {data['version']}")
            
            # 导入配置
            if 'recording' in data:
                self._update_dataclass(self.recording, data['recording'])
            
            if 'anti_detection' in data:
                self._update_dataclass(self.anti_detection, data['anti_detection'])
            
            if 'ui' in data:
                self._update_dataclass(self.ui, data['ui'])
            
            if 'advanced' in data:
                self._update_dataclass(self.advanced, data['advanced'])
            
            # 验证导入的配置
            errors = self.validate_config()
            if errors:
                logger.warning(f"导入的配置有问题: {errors}")
            
            logger.info(f"配置已从 {filename} 导入")
            return True
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def get_recording_settings(self):
        """获取录制设置字典"""
        return {
            'method': self.recording.method,
            'fps': self.recording.fps,
            'quality': self.recording.quality,
            'output_format': self.recording.output_format,
            'output_dir': self.recording.output_dir,
            'filename_template': self.recording.filename_template
        }
    
    def get_anti_detection_settings(self):
        """获取反检测设置字典"""
        return {
            'stealth_mode': self.anti_detection.stealth_mode,
            'hide_window': self.anti_detection.hide_window,
            'fake_process': self.anti_detection.fake_process,
            'process_disguise': self.anti_detection.process_disguise,
            'api_hook': self.anti_detection.api_hook,
            'memory_obfuscation': self.anti_detection.memory_obfuscation,
            'timing_randomization': self.anti_detection.timing_randomization,
            'adaptive_mode': self.anti_detection.adaptive_mode
        }
    
    def update_recording_setting(self, key, value):
        """更新录制设置"""
        if hasattr(self.recording, key):
            setattr(self.recording, key, value)
            if self.ui.auto_save_settings:
                self.save_config()
            return True
        return False
    
    def update_anti_detection_setting(self, key, value):
        """更新反检测设置"""
        if hasattr(self.anti_detection, key):
            setattr(self.anti_detection, key, value)
            if self.ui.auto_save_settings:
                self.save_config()
            return True
        return False
    
    def create_backup(self):
        """创建配置备份"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{self.config_file}.backup_{timestamp}"
            
            if os.path.exists(self.config_file):
                import shutil
                shutil.copy2(self.config_file, backup_file)
                
                # 清理旧备份
                self._cleanup_old_backups()
                
                logger.info(f"配置备份已创建: {backup_file}")
                return backup_file
            
        except Exception as e:
            logger.error(f"创建配置备份失败: {e}")
        
        return None
    
    def _cleanup_old_backups(self):
        """清理旧的配置备份"""
        try:
            import glob
            backup_pattern = f"{self.config_file}.backup_*"
            backups = sorted(glob.glob(backup_pattern), reverse=True)
            
            # 保留最新的几个备份
            for backup in backups[self.advanced.backup_count:]:
                try:
                    os.remove(backup)
                    logger.info(f"已删除旧备份: {backup}")
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"清理备份失败: {e}")


class INIConfigManager:
    """INI格式配置管理器（备用）"""
    
    def __init__(self, config_file="recorder_config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载INI配置"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                self._create_default_ini()
        except Exception as e:
            logger.error(f"加载INI配置失败: {e}")
    
    def _create_default_ini(self):
        """创建默认INI配置"""
        self.config['Recording'] = {
            'method': 'mss',
            'fps': '30',
            'quality': '80',
            'output_format': 'mp4'
        }
        
        self.config['AntiDetection'] = {
            'stealth_mode': 'true',
            'hide_window': 'true',
            'fake_process': 'false'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存INI配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            return True
        except Exception as e:
            logger.error(f"保存INI配置失败: {e}")
            return False
    
    def get(self, section, key, fallback=None):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)
    
    def set(self, section, key, value):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))


if __name__ == "__main__":
    # 测试配置管理器
    print("测试配置管理器...")
    
    config = ConfigManager()
    
    print("当前录制配置:")
    print(f"  方法: {config.recording.method}")
    print(f"  帧率: {config.recording.fps}")
    print(f"  质量: {config.recording.quality}")
    
    print("\n当前反检测配置:")
    print(f"  隐身模式: {config.anti_detection.stealth_mode}")
    print(f"  隐藏窗口: {config.anti_detection.hide_window}")
    
    # 验证配置
    errors = config.validate_config()
    if errors:
        print(f"\n配置错误: {errors}")
    else:
        print("\n配置验证通过")
    
    # 保存配置
    if config.save_config():
        print("配置保存成功")
    
    print("测试完成")
