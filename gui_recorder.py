#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录屏程序GUI界面
提供用户友好的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import os
from datetime import datetime
import json
from screen_recorder import AntiDetectionRecorder
from anti_detection import AntiDetectionManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RecorderGUI:
    """录屏程序GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("高级录屏程序 v1.0")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 初始化录屏器和反检测管理器
        self.recorder = AntiDetectionRecorder()
        self.anti_detection = AntiDetectionManager()
        
        # 状态变量
        self.recording_time = 0
        self.timer_running = False
        
        # 创建界面
        self.create_widgets()
        self.load_settings()
        
        # 启动状态更新
        self.update_status()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="高级录屏程序", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="录制状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="未录制", foreground="red")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="录制时间:").grid(row=1, column=0, sticky=tk.W)
        self.time_label = ttk.Label(status_frame, text="00:00:00")
        self.time_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="输出文件:").grid(row=2, column=0, sticky=tk.W)
        self.file_label = ttk.Label(status_frame, text="未设置")
        self.file_label.grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 录制控制
        control_frame = ttk.LabelFrame(main_frame, text="录制控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        
        self.start_button = ttk.Button(control_frame, text="开始录制", 
                                      command=self.start_recording)
        self.start_button.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))
        
        self.stop_button = ttk.Button(control_frame, text="停止录制", 
                                     command=self.stop_recording, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(5, 0), sticky=(tk.W, tk.E))
        
        # 文件设置
        file_frame = ttk.LabelFrame(main_frame, text="文件设置", padding="10")
        file_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="输出文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_entry = ttk.Entry(file_frame)
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        
        ttk.Button(file_frame, text="浏览",
                  command=self.browse_file).grid(row=0, column=2)

        # 移除录制时长设置，改为手动停止
        # ttk.Label(file_frame, text="录制时长(秒):").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        # self.duration_var = tk.StringVar(value="")
        # duration_entry = ttk.Entry(file_frame, textvariable=self.duration_var, width=10)
        # duration_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 录制设置
        settings_frame = ttk.LabelFrame(main_frame, text="录制设置", padding="10")
        settings_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        ttk.Label(settings_frame, text="截屏方法:").grid(row=0, column=0, sticky=tk.W)
        self.method_var = tk.StringVar(value="mss")
        method_combo = ttk.Combobox(settings_frame, textvariable=self.method_var,
                                   values=["mss", "gdi", "bitblt", "directx"],
                                   state="readonly", width=15)
        method_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        method_combo.bind('<<ComboboxSelected>>', self.on_method_change)

        # DRM绕过选项
        self.drm_bypass_var = tk.BooleanVar(value=True)
        drm_check = ttk.Checkbutton(settings_frame, text="启用DRM绕过",
                                   variable=self.drm_bypass_var,
                                   command=self.on_drm_bypass_change)
        drm_check.grid(row=0, column=4, sticky=tk.W, padx=(20, 0))
        
        ttk.Label(settings_frame, text="帧率:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.fps_var = tk.StringVar(value="30")
        fps_spin = ttk.Spinbox(settings_frame, from_=1, to=60, textvariable=self.fps_var,
                              width=10, command=self.on_fps_change)
        fps_spin.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(settings_frame, text="质量:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.quality_var = tk.StringVar(value="80")
        quality_spin = ttk.Spinbox(settings_frame, from_=1, to=100, textvariable=self.quality_var,
                                  width=10, command=self.on_quality_change)
        quality_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 反检测设置
        anti_frame = ttk.LabelFrame(main_frame, text="反检测设置", padding="10")
        anti_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.stealth_var = tk.BooleanVar(value=True)
        stealth_check = ttk.Checkbutton(anti_frame, text="启用隐身模式", 
                                       variable=self.stealth_var,
                                       command=self.on_stealth_change)
        stealth_check.grid(row=0, column=0, sticky=tk.W)
        
        self.hide_window_var = tk.BooleanVar(value=False)
        hide_check = ttk.Checkbutton(anti_frame, text="隐藏控制台窗口",
                                    variable=self.hide_window_var)
        hide_check.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        self.fake_process_var = tk.BooleanVar(value=False)
        fake_check = ttk.Checkbutton(anti_frame, text="创建假进程(已禁用)",
                                    variable=self.fake_process_var, state="disabled")
        fake_check.grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        
        # 检测信息
        detection_frame = ttk.LabelFrame(main_frame, text="检测信息", padding="10")
        detection_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        detection_frame.columnconfigure(0, weight=1)
        
        self.detection_text = tk.Text(detection_frame, height=4, width=50)
        self.detection_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        detection_scroll = ttk.Scrollbar(detection_frame, orient="vertical", 
                                        command=self.detection_text.yview)
        detection_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.detection_text.configure(yscrollcommand=detection_scroll.set)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="检测反录屏软件", 
                  command=self.detect_software).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="启用所有保护", 
                  command=self.enable_all_protections).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="保存设置", 
                  command=self.save_settings).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="关于", 
                  command=self.show_about).pack(side=tk.LEFT)
    
    def start_recording(self):
        """开始录制"""
        try:
            # 获取设置
            output_file = self.file_entry.get().strip()
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"screen_record_{timestamp}.avi"  # 使用更兼容的AVI格式
                self.file_entry.delete(0, tk.END)
                self.file_entry.insert(0, output_file)

            # 移除录制时长设置，改为手动停止
            # duration_str = self.duration_var.get().strip()
            # duration = int(duration_str) if duration_str else None

            # 应用设置
            self.recorder.set_method(self.method_var.get())
            self.recorder.set_fps(int(self.fps_var.get()))
            self.recorder.set_quality(int(self.quality_var.get()))

            # 启用反检测（但不隐藏GUI界面）
            if self.stealth_var.get():
                # 只启用不会影响GUI界面的反检测功能
                self.anti_detection._change_process_name()  # 只修改进程名
                if self.hide_window_var.get():
                    self.anti_detection._hide_console_only()  # 只隐藏控制台
                self.anti_detection.modify_memory_signatures()
                self.anti_detection.randomize_timing()

            # 开始录制（无时长限制，手动停止）
            if self.recorder.start_recording(output_file, None):
                self.start_button.config(state="disabled")
                self.stop_button.config(state="normal")
                self.recording_time = 0
                self.timer_running = True
                self.file_label.config(text=output_file)

                self.log_message("录制已开始（手动停止）")
            else:
                messagebox.showerror("错误", "无法开始录制")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"开始录制失败: {e}")
    
    def stop_recording(self):
        """停止录制"""
        try:
            if self.recorder.stop_recording():
                self.start_button.config(state="normal")
                self.stop_button.config(state="disabled")
                self.timer_running = False
                
                self.log_message("录制已停止")
                messagebox.showinfo("完成", "录制已完成")
            else:
                messagebox.showwarning("警告", "当前没有在录制")
                
        except Exception as e:
            messagebox.showerror("错误", f"停止录制失败: {e}")
    
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".avi",
            filetypes=[("AVI files", "*.avi"), ("MP4 files", "*.mp4"), ("All files", "*.*")]
        )
        if filename:
            self.file_entry.delete(0, tk.END)
            self.file_entry.insert(0, filename)
    
    def on_method_change(self, event=None):
        """截屏方法改变"""
        self.recorder.set_method(self.method_var.get())

    def on_drm_bypass_change(self):
        """DRM绕过选项改变"""
        self.recorder.enable_drm_bypass(self.drm_bypass_var.get())

    def on_fps_change(self):
        """帧率改变"""
        try:
            fps = int(self.fps_var.get())
            self.recorder.set_fps(fps)
        except ValueError:
            pass

    def on_quality_change(self):
        """质量改变"""
        try:
            quality = int(self.quality_var.get())
            self.recorder.set_quality(quality)
        except ValueError:
            pass
    
    def on_stealth_change(self):
        """隐身模式改变"""
        if self.stealth_var.get():
            self.anti_detection.adaptive_stealth_mode()
        else:
            self.anti_detection.cleanup()
    
    def detect_software(self):
        """检测反录屏软件"""
        try:
            detected = self.anti_detection.detect_anti_recording_software()
            if detected:
                message = f"检测到反录屏软件: {', '.join(detected)}"
                self.log_message(message)
                messagebox.showwarning("检测结果", message)
            else:
                message = "未检测到反录屏软件"
                self.log_message(message)
                messagebox.showinfo("检测结果", message)
        except Exception as e:
            messagebox.showerror("错误", f"检测失败: {e}")
    
    def enable_all_protections(self):
        """启用所有保护"""
        try:
            self.anti_detection.enable_all_protections()
            self.log_message("所有反检测保护已启用")
            messagebox.showinfo("完成", "所有反检测保护已启用")
        except Exception as e:
            messagebox.showerror("错误", f"启用保护失败: {e}")
    
    def log_message(self, message):
        """记录消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.detection_text.insert(tk.END, log_entry)
        self.detection_text.see(tk.END)
    
    def update_status(self):
        """更新状态"""
        try:
            # 更新录制状态
            if self.recorder.is_recording():
                self.status_label.config(text="录制中", foreground="green")
                if self.timer_running:
                    self.recording_time += 1
            else:
                self.status_label.config(text="未录制", foreground="red")
                self.timer_running = False
            
            # 更新时间显示
            hours = self.recording_time // 3600
            minutes = (self.recording_time % 3600) // 60
            seconds = self.recording_time % 60
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.time_label.config(text=time_str)
            
        except Exception as e:
            logger.error(f"状态更新失败: {e}")
        
        # 每秒更新一次
        self.root.after(1000, self.update_status)
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = {
                'method': self.method_var.get(),
                'fps': self.fps_var.get(),
                'quality': self.quality_var.get(),
                'stealth': self.stealth_var.get(),
                'hide_window': self.hide_window_var.get(),
                'fake_process': self.fake_process_var.get()
            }
            
            with open('recorder_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo("完成", "设置已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def load_settings(self):
        """加载设置"""
        try:
            if os.path.exists('recorder_settings.json'):
                with open('recorder_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                self.method_var.set(settings.get('method', 'mss'))
                self.fps_var.set(settings.get('fps', '30'))
                self.quality_var.set(settings.get('quality', '80'))
                self.stealth_var.set(settings.get('stealth', True))
                self.hide_window_var.set(settings.get('hide_window', False))
                self.fake_process_var.set(settings.get('fake_process', False))
                
        except Exception as e:
            logger.warning(f"加载设置失败: {e}")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """高级录屏程序 v1.0

功能特点:
• 多种截屏方法 (MSS, GDI, BitBlt, DirectX)
• 反检测技术
• 进程隐藏和伪装
• 自适应隐身模式
• 用户友好的GUI界面

作者: AI Assistant
版本: 1.0"""
        
        messagebox.showinfo("关于", about_text)
    
    def run(self):
        """运行GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            logger.error(f"GUI运行失败: {e}")
    
    def on_closing(self):
        """关闭程序"""
        try:
            if self.recorder.is_recording():
                if messagebox.askokcancel("退出", "正在录制中，确定要退出吗？"):
                    self.recorder.stop_recording()
                    self.anti_detection.cleanup()
                    self.root.destroy()
            else:
                self.anti_detection.cleanup()
                self.root.destroy()
        except Exception as e:
            logger.error(f"关闭程序失败: {e}")
            self.root.destroy()


if __name__ == "__main__":
    try:
        app = RecorderGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        input("按回车键退出...")
