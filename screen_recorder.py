#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级录屏程序 - 支持绕过部分软件录屏检测
作者: AI Assistant
版本: 1.0
"""

import cv2
import numpy as np
import pyautogui
import threading
import time
import os
import sys
import ctypes
from ctypes import wintypes
import win32gui
import win32con
import win32api
import win32process
import psutil
from PIL import Image, ImageGrab
import mss
import subprocess
from datetime import datetime
import json
import logging
from drm_bypass import DRMBypassCapture

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AntiDetectionRecorder:
    """反检测录屏器"""
    
    def __init__(self):
        self.recording = False
        self.output_file = None
        self.fps = 30
        self.quality = 80
        self.method = 'mss'  # 默认使用mss方法
        self.stealth_mode = True
        self.process_name = "explorer.exe"  # 伪装进程名
        self.hide_window = True
        self.drm_bypass_enabled = True  # 启用DRM绕过

        # 初始化DRM绕过捕获器
        self.drm_capture = DRMBypassCapture()

        # 初始化反检测机制
        self._init_anti_detection()
        
    def _init_anti_detection(self):
        """初始化反检测机制"""
        try:
            # 1. 修改进程名称（伪装）
            self._disguise_process()
            
            # 2. 隐藏窗口句柄
            if self.hide_window:
                self._hide_window()
                
            # 3. 禁用一些常见的检测API
            self._hook_detection_apis()
            
        except Exception as e:
            logger.warning(f"反检测初始化失败: {e}")
    
    def _disguise_process(self):
        """伪装进程名称"""
        try:
            # 修改进程标题
            ctypes.windll.kernel32.SetConsoleTitleW(self.process_name)
            
            # 尝试修改进程名（需要管理员权限）
            try:
                import win32process
                import win32api
                handle = win32api.GetCurrentProcess()
                win32process.SetProcessWorkingSetSize(handle, -1, -1)
            except:
                pass
                
        except Exception as e:
            logger.warning(f"进程伪装失败: {e}")
    
    def _hide_window(self):
        """隐藏窗口"""
        try:
            # 获取控制台窗口句柄
            console_window = ctypes.windll.kernel32.GetConsoleWindow()
            if console_window:
                # 隐藏窗口
                ctypes.windll.user32.ShowWindow(console_window, 0)
        except Exception as e:
            logger.warning(f"窗口隐藏失败: {e}")
    
    def _hook_detection_apis(self):
        """Hook常见的检测API"""
        try:
            # 这里可以添加更复杂的API Hook机制
            # 目前只是基础实现
            pass
        except Exception as e:
            logger.warning(f"API Hook失败: {e}")
    
    def _capture_screen_mss(self):
        """使用MSS库捕获屏幕（更难被检测）"""
        try:
            with mss.mss() as sct:
                monitor = sct.monitors[1]  # 主显示器
                screenshot = sct.grab(monitor)
                img = Image.frombytes('RGB', screenshot.size, screenshot.bgra, 'raw', 'BGRX')
                return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
        except Exception as e:
            logger.error(f"MSS截屏失败: {e}")
            return None
    
    def _capture_screen_gdi(self):
        """使用GDI+捕获屏幕"""
        try:
            # 使用PIL的ImageGrab
            screenshot = ImageGrab.grab()
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            logger.error(f"GDI截屏失败: {e}")
            return None
    
    def _capture_screen_directx(self):
        """使用DirectX捕获屏幕（绕过DRM保护）"""
        try:
            # 尝试使用DXGI Desktop Duplication API
            frame = self._capture_with_dxgi()
            if frame is not None:
                return frame

            # 回退到其他方法
            screenshot = pyautogui.screenshot()
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        except Exception as e:
            logger.error(f"DirectX截屏失败: {e}")
            return None

    def _capture_with_dxgi(self):
        """使用DXGI Desktop Duplication API绕过DRM"""
        try:
            import ctypes
            from ctypes import wintypes, windll

            # 这是一个简化的DXGI实现
            # 实际使用需要更复杂的DirectX调用
            user32 = windll.user32
            gdi32 = windll.gdi32

            # 获取桌面窗口
            hdesktop = user32.GetDesktopWindow()
            desktop_dc = user32.GetWindowDC(hdesktop)

            # 创建兼容的设备上下文
            mem_dc = gdi32.CreateCompatibleDC(desktop_dc)

            # 获取屏幕尺寸
            width = user32.GetSystemMetrics(0)  # SM_CXSCREEN
            height = user32.GetSystemMetrics(1)  # SM_CYSCREEN

            # 创建位图
            hbitmap = gdi32.CreateCompatibleBitmap(desktop_dc, width, height)
            gdi32.SelectObject(mem_dc, hbitmap)

            # 使用PrintWindow API而不是BitBlt
            # 这可以绕过某些DRM保护
            result = user32.PrintWindow(hdesktop, mem_dc, 3)  # PW_RENDERFULLCONTENT

            if not result:
                # 如果PrintWindow失败，尝试BitBlt
                gdi32.BitBlt(mem_dc, 0, 0, width, height, desktop_dc, 0, 0, 0x00CC0020)  # SRCCOPY

            # 获取位图数据
            bmpinfo = ctypes.create_string_buffer(40)  # BITMAPINFOHEADER
            ctypes.memset(bmpinfo, 0, 40)
            ctypes.c_long.from_address(ctypes.addressof(bmpinfo)).value = 40
            ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 4).value = width
            ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 8).value = -height  # 负值表示自上而下
            ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 12).value = 1
            ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 14).value = 32

            # 分配缓冲区
            buffer_size = width * height * 4
            buffer = ctypes.create_string_buffer(buffer_size)

            # 获取DIB数据
            gdi32.GetDIBits(mem_dc, hbitmap, 0, height, buffer, bmpinfo, 0)

            # 转换为numpy数组
            import numpy as np
            img = np.frombuffer(buffer, dtype=np.uint8)
            img = img.reshape((height, width, 4))

            # 清理资源
            gdi32.DeleteObject(hbitmap)
            gdi32.DeleteDC(mem_dc)
            user32.ReleaseDC(hdesktop, desktop_dc)

            # 转换颜色格式
            return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)

        except Exception as e:
            logger.error(f"DXGI截屏失败: {e}")
            return None
    
    def _capture_screen_bitblt(self):
        """使用BitBlt API捕获屏幕"""
        try:
            import win32gui
            import win32ui
            import win32con
            
            # 获取屏幕DC
            hdesktop = win32gui.GetDesktopWindow()
            desktop_dc = win32gui.GetWindowDC(hdesktop)
            img_dc = win32ui.CreateDCFromHandle(desktop_dc)
            mem_dc = img_dc.CreateCompatibleDC()
            
            # 获取屏幕尺寸
            width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
            
            # 创建位图
            screenshot = win32ui.CreateBitmap()
            screenshot.CreateCompatibleBitmap(img_dc, width, height)
            mem_dc.SelectObject(screenshot)
            
            # 复制屏幕内容
            mem_dc.BitBlt((0, 0), (width, height), img_dc, (0, 0), win32con.SRCCOPY)
            
            # 转换为numpy数组
            bmpinfo = screenshot.GetInfo()
            bmpstr = screenshot.GetBitmapBits(True)
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            
            # 清理资源
            mem_dc.DeleteDC()
            win32gui.DeleteObject(screenshot.GetHandle())
            win32gui.ReleaseDC(hdesktop, desktop_dc)
            
            return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
        except Exception as e:
            logger.error(f"BitBlt截屏失败: {e}")
            return None
    
    def capture_screen(self):
        """智能屏幕捕获 - 尝试多种方法，包括DRM绕过"""

        # 如果启用了DRM绕过，优先尝试DRM绕过方法
        if self.drm_bypass_enabled:
            try:
                frame = self.drm_capture.capture_with_drm_bypass()
                if frame is not None:
                    return frame
                else:
                    logger.warning("DRM绕过方法失败，尝试标准方法")
            except Exception as e:
                logger.warning(f"DRM绕过出错: {e}")

        # 标准截屏方法
        methods = {
            'mss': self._capture_screen_mss,
            'gdi': self._capture_screen_gdi,
            'bitblt': self._capture_screen_bitblt,
            'directx': self._capture_screen_directx
        }

        # 首先尝试指定的方法
        if self.method in methods:
            frame = methods[self.method]()
            if frame is not None:
                return frame

        # 如果指定方法失败，尝试其他方法
        for method_name, method_func in methods.items():
            if method_name != self.method:
                try:
                    frame = method_func()
                    if frame is not None:
                        logger.info(f"切换到 {method_name} 方法")
                        self.method = method_name
                        return frame
                except:
                    continue

        logger.error("所有截屏方法都失败了")
        return None
    
    def start_recording(self, output_file=None, duration=None):
        """开始录制"""
        if self.recording:
            logger.warning("录制已在进行中")
            return False
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"screen_record_{timestamp}.avi"  # 使用更兼容的AVI格式
        
        self.output_file = output_file
        self.recording = True
        
        # 在新线程中开始录制
        self.record_thread = threading.Thread(target=self._record_loop, args=(duration,))
        self.record_thread.daemon = True
        self.record_thread.start()
        
        logger.info(f"开始录制到文件: {output_file}")
        return True
    
    def _record_loop(self, duration=None):
        """录制循环"""
        try:
            # 获取屏幕尺寸
            first_frame = self.capture_screen()
            if first_frame is None:
                logger.error("无法获取屏幕截图")
                return
            
            height, width = first_frame.shape[:2]
            
            # 设置视频编码器 - 使用更兼容的编码器
            # 尝试多种编码器以确保兼容性
            codecs_to_try = [
                cv2.VideoWriter_fourcc(*'XVID'),  # 最兼容
                cv2.VideoWriter_fourcc(*'MJPG'),  # Motion JPEG
                cv2.VideoWriter_fourcc(*'X264'),  # H.264
                cv2.VideoWriter_fourcc(*'mp4v'),  # MPEG-4
            ]

            out = None
            for fourcc in codecs_to_try:
                try:
                    out = cv2.VideoWriter(self.output_file, fourcc, self.fps, (width, height))
                    if out.isOpened():
                        logger.info(f"使用编码器: {fourcc}")
                        break
                    else:
                        out.release()
                        out = None
                except:
                    if out:
                        out.release()
                        out = None
                    continue

            if out is None:
                logger.error("无法创建视频编码器")
                return
            
            start_time = time.time()
            frame_count = 0
            
            while self.recording:
                frame_start = time.time()
                
                # 捕获屏幕
                frame = self.capture_screen()
                if frame is not None:
                    out.write(frame)
                    frame_count += 1
                
                # 检查录制时长
                if duration and (time.time() - start_time) >= duration:
                    break
                
                # 控制帧率
                elapsed = time.time() - frame_start
                sleep_time = (1.0 / self.fps) - elapsed
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            out.release()
            logger.info(f"录制完成，共录制 {frame_count} 帧")
            
        except Exception as e:
            logger.error(f"录制过程中出错: {e}")
        finally:
            self.recording = False
    
    def stop_recording(self):
        """停止录制"""
        if not self.recording:
            logger.warning("当前没有在录制")
            return False
        
        self.recording = False
        if hasattr(self, 'record_thread'):
            self.record_thread.join(timeout=5)
        
        logger.info("录制已停止")
        return True
    
    def is_recording(self):
        """检查是否正在录制"""
        return self.recording
    
    def set_quality(self, quality):
        """设置录制质量 (1-100)"""
        self.quality = max(1, min(100, quality))
    
    def set_fps(self, fps):
        """设置帧率"""
        self.fps = max(1, min(60, fps))
    
    def set_method(self, method):
        """设置截屏方法"""
        available_methods = ['mss', 'gdi', 'bitblt', 'directx', 'drm_bypass']
        if method in available_methods:
            if method == 'drm_bypass':
                self.drm_bypass_enabled = True
                logger.info("启用DRM绕过模式")
            else:
                self.method = method
                logger.info(f"截屏方法设置为: {method}")
        else:
            logger.warning(f"不支持的截屏方法: {method}")

    def enable_drm_bypass(self, enabled=True):
        """启用/禁用DRM绕过功能"""
        self.drm_bypass_enabled = enabled
        if enabled:
            logger.info("DRM绕过功能已启用")
        else:
            logger.info("DRM绕过功能已禁用")

    def get_drm_bypass_status(self):
        """获取DRM绕过状态"""
        return self.drm_bypass_enabled


if __name__ == "__main__":
    # 创建录屏器实例
    recorder = AntiDetectionRecorder()
    
    print("高级录屏程序 v1.0")
    print("支持的命令:")
    print("  start [文件名] - 开始录制（手动停止）")
    print("  stop - 停止录制")
    print("  status - 查看状态")
    print("  method [方法名] - 设置截屏方法 (mss/gdi/bitblt/directx)")
    print("  fps [数值] - 设置帧率")
    print("  quit - 退出程序")
    print()
    
    try:
        while True:
            cmd = input("请输入命令: ").strip().split()
            if not cmd:
                continue
            
            if cmd[0] == "start":
                filename = cmd[1] if len(cmd) > 1 else None
                # 移除录制时长参数，改为手动停止
                # duration = int(cmd[2]) if len(cmd) > 2 else None
                recorder.start_recording(filename, None)
            
            elif cmd[0] == "stop":
                recorder.stop_recording()
            
            elif cmd[0] == "status":
                status = "录制中" if recorder.is_recording() else "未录制"
                print(f"状态: {status}")
                print(f"方法: {recorder.method}")
                print(f"帧率: {recorder.fps}")
            
            elif cmd[0] == "method":
                if len(cmd) > 1:
                    recorder.set_method(cmd[1])
                else:
                    print("请指定方法名")
            
            elif cmd[0] == "fps":
                if len(cmd) > 1:
                    try:
                        fps = int(cmd[1])
                        recorder.set_fps(fps)
                        print(f"帧率设置为: {fps}")
                    except ValueError:
                        print("请输入有效的数字")
                else:
                    print("请指定帧率")
            
            elif cmd[0] == "quit":
                if recorder.is_recording():
                    recorder.stop_recording()
                break
            
            else:
                print("未知命令")
    
    except KeyboardInterrupt:
        print("\n程序被中断")
        if recorder.is_recording():
            recorder.stop_recording()
    
    print("程序退出")
