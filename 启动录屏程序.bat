@echo off
chcp 65001 >nul
title 高级录屏程序启动器

echo ================================================
echo 高级录屏程序启动器 v1.0
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 显示Python版本
echo 检测到Python版本:
python --version

REM 检查是否在正确目录
if not exist "gui_recorder.py" (
    echo 错误: 未找到程序文件，请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo.
echo 正在启动录屏程序...
echo.

REM 启动Python启动器
python start_recorder.py

REM 如果启动器失败，提供备选方案
if errorlevel 1 (
    echo.
    echo 启动器失败，请选择启动方式:
    echo 1. GUI界面模式
    echo 2. 命令行模式
    echo 3. 退出
    echo.
    set /p choice="请输入选择 (1-3): "
    
    if "%choice%"=="1" (
        echo 启动GUI模式...
        python gui_recorder.py
    ) else if "%choice%"=="2" (
        echo 启动命令行模式...
        python screen_recorder.py
    ) else (
        echo 退出程序
    )
)

echo.
echo 程序已结束
pause
