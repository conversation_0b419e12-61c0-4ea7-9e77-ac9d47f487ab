#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DRM绕过模块 - 专门用于绕过视频播放器的反录屏保护
支持多种高级截屏技术来绕过DRM保护
"""

import cv2
import numpy as np
import time
import logging
import ctypes
from ctypes import wintypes, windll
import threading
from PIL import Image, ImageGrab
import pyautogui

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DRMBypassCapture:
    """DRM绕过截屏器"""
    
    def __init__(self):
        self.methods = [
            'dxgi_duplication',
            'mirror_driver', 
            'hardware_overlay',
            'memory_injection',
            'window_composition',
            'printscreen_hook',
            'gdi_plus_enhanced'
        ]
        self.current_method = 0
        self.success_method = None
        
    def capture_with_drm_bypass(self):
        """使用DRM绕过技术捕获屏幕"""
        
        # 如果之前有成功的方法，优先使用
        if self.success_method:
            frame = self._call_method(self.success_method)
            if frame is not None:
                return frame
            else:
                # 成功方法失效，重置
                self.success_method = None
        
        # 尝试所有方法
        for method in self.methods:
            try:
                frame = self._call_method(method)
                if frame is not None:
                    self.success_method = method
                    logger.info(f"DRM绕过成功，使用方法: {method}")
                    return frame
            except Exception as e:
                logger.debug(f"方法 {method} 失败: {e}")
                continue
        
        logger.warning("所有DRM绕过方法都失败了")
        return None
    
    def _call_method(self, method_name):
        """调用指定的截屏方法"""
        method_map = {
            'dxgi_duplication': self._dxgi_desktop_duplication,
            'mirror_driver': self._mirror_driver_capture,
            'hardware_overlay': self._hardware_overlay_capture,
            'memory_injection': self._memory_injection_capture,
            'window_composition': self._window_composition_capture,
            'printscreen_hook': self._printscreen_hook_capture,
            'gdi_plus_enhanced': self._gdi_plus_enhanced_capture
        }
        
        if method_name in method_map:
            return method_map[method_name]()
        return None
    
    def _dxgi_desktop_duplication(self):
        """DXGI Desktop Duplication API - 最难被检测的方法"""
        try:
            # 这需要DirectX 11.1+
            # 使用ctypes调用DXGI API
            
            # 获取DXGI工厂
            dxgi = windll.LoadLibrary("dxgi.dll")
            d3d11 = windll.LoadLibrary("d3d11.dll")
            
            # 简化实现 - 实际需要更复杂的DirectX调用
            # 这里使用替代方法
            return self._advanced_gdi_capture()
            
        except Exception as e:
            logger.debug(f"DXGI Desktop Duplication失败: {e}")
            return None
    
    def _mirror_driver_capture(self):
        """镜像驱动捕获 - 绕过硬件级保护"""
        try:
            # 模拟镜像驱动的行为
            # 使用多重DC创建来绕过检测
            
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            # 获取多个显示器信息
            monitors = []
            
            def monitor_enum_proc(hmonitor, hdc, lprect, lparam):
                monitors.append(hmonitor)
                return True
            
            MONITORENUMPROC = ctypes.WINFUNCTYPE(ctypes.c_bool, 
                                               wintypes.HMONITOR,
                                               wintypes.HDC,
                                               ctypes.POINTER(wintypes.RECT),
                                               wintypes.LPARAM)
            
            user32.EnumDisplayMonitors(None, None, MONITORENUMPROC(monitor_enum_proc), 0)
            
            # 使用第一个监视器
            if monitors:
                return self._capture_monitor_direct(monitors[0])
            
            return None
            
        except Exception as e:
            logger.debug(f"镜像驱动捕获失败: {e}")
            return None
    
    def _hardware_overlay_capture(self):
        """硬件覆盖层捕获"""
        try:
            # 尝试绕过硬件覆盖保护
            # 使用不同的颜色空间和位深度
            
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            hdc = user32.GetDC(0)
            width = user32.GetSystemMetrics(0)
            height = user32.GetSystemMetrics(1)
            
            # 创建多个兼容DC
            mem_dc1 = gdi32.CreateCompatibleDC(hdc)
            mem_dc2 = gdi32.CreateCompatibleDC(hdc)
            
            # 使用不同的位图格式
            bmi = ctypes.create_string_buffer(40)
            ctypes.memset(bmi, 0, 40)
            ctypes.c_long.from_address(ctypes.addressof(bmi)).value = 40
            ctypes.c_long.from_address(ctypes.addressof(bmi) + 4).value = width
            ctypes.c_long.from_address(ctypes.addressof(bmi) + 8).value = -height
            ctypes.c_short.from_address(ctypes.addressof(bmi) + 12).value = 1
            ctypes.c_short.from_address(ctypes.addressof(bmi) + 14).value = 32
            
            bits = ctypes.c_void_p()
            hbitmap = gdi32.CreateDIBSection(mem_dc1, bmi, 0, ctypes.byref(bits), None, 0)
            
            if hbitmap:
                old_bitmap = gdi32.SelectObject(mem_dc1, hbitmap)
                
                # 使用特殊的复制模式
                success = gdi32.BitBlt(mem_dc1, 0, 0, width, height, hdc, 0, 0, 0x00CC0020)
                
                if success and bits.value:
                    buffer_size = width * height * 4
                    buffer = (ctypes.c_ubyte * buffer_size).from_address(bits.value)
                    img = np.frombuffer(buffer, dtype=np.uint8)
                    img = img.reshape((height, width, 4))
                    
                    gdi32.SelectObject(mem_dc1, old_bitmap)
                    gdi32.DeleteObject(hbitmap)
                    gdi32.DeleteDC(mem_dc1)
                    gdi32.DeleteDC(mem_dc2)
                    user32.ReleaseDC(0, hdc)
                    
                    return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                
                gdi32.SelectObject(mem_dc1, old_bitmap)
                gdi32.DeleteObject(hbitmap)
            
            gdi32.DeleteDC(mem_dc1)
            gdi32.DeleteDC(mem_dc2)
            user32.ReleaseDC(0, hdc)
            
            return None
            
        except Exception as e:
            logger.debug(f"硬件覆盖层捕获失败: {e}")
            return None
    
    def _memory_injection_capture(self):
        """内存注入捕获 - 直接读取显存"""
        try:
            # 尝试直接访问显存
            # 这是一个高级技术，需要管理员权限
            
            # 获取当前进程句柄
            kernel32 = windll.kernel32
            process_handle = kernel32.GetCurrentProcess()
            
            # 尝试分配共享内存
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            width = user32.GetSystemMetrics(0)
            height = user32.GetSystemMetrics(1)
            
            # 创建文件映射
            size = width * height * 4
            mapping = kernel32.CreateFileMappingW(
                0xFFFFFFFF,  # INVALID_HANDLE_VALUE
                None,
                0x04,  # PAGE_READWRITE
                0,
                size,
                None
            )
            
            if mapping:
                # 映射视图
                view = kernel32.MapViewOfFile(
                    mapping,
                    0x0006,  # FILE_MAP_READ | FILE_MAP_WRITE
                    0, 0, size
                )
                
                if view:
                    # 尝试直接复制屏幕数据到映射内存
                    hdc = user32.GetDC(0)
                    mem_dc = gdi32.CreateCompatibleDC(hdc)
                    
                    # 创建DIB section指向映射内存
                    bmi = ctypes.create_string_buffer(40)
                    ctypes.memset(bmi, 0, 40)
                    ctypes.c_long.from_address(ctypes.addressof(bmi)).value = 40
                    ctypes.c_long.from_address(ctypes.addressof(bmi) + 4).value = width
                    ctypes.c_long.from_address(ctypes.addressof(bmi) + 8).value = -height
                    ctypes.c_short.from_address(ctypes.addressof(bmi) + 12).value = 1
                    ctypes.c_short.from_address(ctypes.addressof(bmi) + 14).value = 32
                    
                    bits = ctypes.c_void_p(view)
                    hbitmap = gdi32.CreateDIBSection(mem_dc, bmi, 0, ctypes.byref(bits), mapping, 0)
                    
                    if hbitmap:
                        old_bitmap = gdi32.SelectObject(mem_dc, hbitmap)
                        success = gdi32.BitBlt(mem_dc, 0, 0, width, height, hdc, 0, 0, 0x00CC0020)
                        
                        if success:
                            # 从映射内存读取数据
                            buffer = (ctypes.c_ubyte * size).from_address(view)
                            img = np.frombuffer(buffer, dtype=np.uint8)
                            img = img.reshape((height, width, 4))
                            
                            result = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                        else:
                            result = None
                        
                        gdi32.SelectObject(mem_dc, old_bitmap)
                        gdi32.DeleteObject(hbitmap)
                    else:
                        result = None
                    
                    gdi32.DeleteDC(mem_dc)
                    user32.ReleaseDC(0, hdc)
                    kernel32.UnmapViewOfFile(view)
                else:
                    result = None
                
                kernel32.CloseHandle(mapping)
                return result
            
            return None
            
        except Exception as e:
            logger.debug(f"内存注入捕获失败: {e}")
            return None
    
    def _window_composition_capture(self):
        """窗口合成捕获 - 逐窗口捕获并合成"""
        try:
            import win32gui
            import win32ui
            import win32con
            
            # 获取屏幕尺寸
            screen_width = win32gui.GetSystemMetrics(win32con.SM_CXSCREEN)
            screen_height = win32gui.GetSystemMetrics(win32con.SM_CYSCREEN)
            
            # 创建合成图像
            composite = np.zeros((screen_height, screen_width, 3), dtype=np.uint8)
            
            # 枚举所有窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    try:
                        rect = win32gui.GetWindowRect(hwnd)
                        if rect[2] - rect[0] > 0 and rect[3] - rect[1] > 0:
                            windows.append(hwnd)
                    except:
                        pass
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            # 按Z顺序排序窗口（底层到顶层）
            windows.reverse()
            
            # 捕获每个窗口
            for hwnd in windows:
                try:
                    rect = win32gui.GetWindowRect(hwnd)
                    x, y, right, bottom = rect
                    width = right - x
                    height = bottom - y
                    
                    if width <= 0 or height <= 0:
                        continue
                    
                    # 使用PrintWindow API捕获窗口
                    hwnd_dc = win32gui.GetWindowDC(hwnd)
                    mem_dc = win32gui.CreateCompatibleDC(hwnd_dc)
                    
                    screenshot = win32ui.CreateBitmap()
                    screenshot.CreateCompatibleBitmap(win32ui.CreateDCFromHandle(hwnd_dc), width, height)
                    mem_dc.SelectObject(screenshot)
                    
                    # 尝试多种PrintWindow模式
                    modes = [0x00000002, 0x00000000, 0x00000001]  # PW_CLIENTONLY, 0, PW_RENDERFULLCONTENT
                    
                    captured = False
                    for mode in modes:
                        if win32gui.PrintWindow(hwnd, mem_dc, mode):
                            captured = True
                            break
                    
                    if captured:
                        bmpinfo = screenshot.GetInfo()
                        bmpstr = screenshot.GetBitmapBits(True)
                        img = np.frombuffer(bmpstr, dtype='uint8')
                        img = img.reshape((height, width, 4))
                        img_bgr = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                        
                        # 合成到总图像
                        end_x = min(x + width, screen_width)
                        end_y = min(y + height, screen_height)
                        start_x = max(0, x)
                        start_y = max(0, y)
                        
                        if start_x < end_x and start_y < end_y:
                            src_start_x = start_x - x
                            src_start_y = start_y - y
                            src_end_x = src_start_x + (end_x - start_x)
                            src_end_y = src_start_y + (end_y - start_y)
                            
                            composite[start_y:end_y, start_x:end_x] = img_bgr[src_start_y:src_end_y, src_start_x:src_end_x]
                    
                    # 清理资源
                    mem_dc.DeleteDC()
                    win32gui.DeleteObject(screenshot.GetHandle())
                    win32gui.ReleaseDC(hwnd, hwnd_dc)
                    
                except Exception as e:
                    continue
            
            return composite
            
        except Exception as e:
            logger.debug(f"窗口合成捕获失败: {e}")
            return None
    
    def _printscreen_hook_capture(self):
        """PrintScreen钩子捕获"""
        try:
            # 模拟按下PrintScreen键
            user32 = windll.user32
            
            # 发送PrintScreen键
            user32.keybd_event(0x2C, 0, 0, 0)  # VK_SNAPSHOT down
            user32.keybd_event(0x2C, 0, 2, 0)  # VK_SNAPSHOT up
            
            # 等待剪贴板更新
            time.sleep(0.1)
            
            # 从剪贴板获取图像
            try:
                img = ImageGrab.grabclipboard()
                if img:
                    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            except:
                pass
            
            return None
            
        except Exception as e:
            logger.debug(f"PrintScreen钩子捕获失败: {e}")
            return None
    
    def _gdi_plus_enhanced_capture(self):
        """增强GDI+捕获"""
        try:
            # 使用PIL的增强捕获
            screenshot = ImageGrab.grab(all_screens=True)
            return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
        except Exception as e:
            logger.debug(f"增强GDI+捕获失败: {e}")
            return None
    
    def _advanced_gdi_capture(self):
        """高级GDI捕获"""
        try:
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            hdc = user32.GetDC(0)
            width = user32.GetSystemMetrics(0)
            height = user32.GetSystemMetrics(1)
            
            mem_dc = gdi32.CreateCompatibleDC(hdc)
            hbitmap = gdi32.CreateCompatibleBitmap(hdc, width, height)
            old_bitmap = gdi32.SelectObject(mem_dc, hbitmap)
            
            # 尝试多种复制模式
            copy_modes = [
                0x00CC0020,  # SRCCOPY
                0x008800C6,  # SRCPAINT  
                0x00660046,  # SRCINVERT
                0x00EE0086,  # SRCAND
            ]
            
            for mode in copy_modes:
                if gdi32.BitBlt(mem_dc, 0, 0, width, height, hdc, 0, 0, mode):
                    # 获取位图数据
                    bmpinfo = ctypes.create_string_buffer(40)
                    ctypes.memset(bmpinfo, 0, 40)
                    ctypes.c_long.from_address(ctypes.addressof(bmpinfo)).value = 40
                    ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 4).value = width
                    ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 8).value = -height
                    ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 12).value = 1
                    ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 14).value = 32
                    
                    buffer_size = width * height * 4
                    buffer = ctypes.create_string_buffer(buffer_size)
                    
                    if gdi32.GetDIBits(mem_dc, hbitmap, 0, height, buffer, bmpinfo, 0):
                        img = np.frombuffer(buffer, dtype=np.uint8)
                        img = img.reshape((height, width, 4))
                        
                        gdi32.SelectObject(mem_dc, old_bitmap)
                        gdi32.DeleteObject(hbitmap)
                        gdi32.DeleteDC(mem_dc)
                        user32.ReleaseDC(0, hdc)
                        
                        return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            gdi32.SelectObject(mem_dc, old_bitmap)
            gdi32.DeleteObject(hbitmap)
            gdi32.DeleteDC(mem_dc)
            user32.ReleaseDC(0, hdc)
            
            return None
            
        except Exception as e:
            logger.debug(f"高级GDI捕获失败: {e}")
            return None
    
    def _capture_monitor_direct(self, monitor_handle):
        """直接捕获指定监视器"""
        try:
            user32 = windll.user32
            gdi32 = windll.gdi32
            
            # 获取监视器信息
            monitor_info = wintypes.RECT()
            user32.GetMonitorInfoW(monitor_handle, ctypes.byref(monitor_info))
            
            width = monitor_info.right - monitor_info.left
            height = monitor_info.bottom - monitor_info.top
            
            # 获取监视器DC
            hdc = user32.GetDC(0)
            mem_dc = gdi32.CreateCompatibleDC(hdc)
            hbitmap = gdi32.CreateCompatibleBitmap(hdc, width, height)
            old_bitmap = gdi32.SelectObject(mem_dc, hbitmap)
            
            # 复制监视器内容
            success = gdi32.BitBlt(mem_dc, 0, 0, width, height, 
                                 hdc, monitor_info.left, monitor_info.top, 0x00CC0020)
            
            if success:
                # 获取位图数据
                bmpinfo = ctypes.create_string_buffer(40)
                ctypes.memset(bmpinfo, 0, 40)
                ctypes.c_long.from_address(ctypes.addressof(bmpinfo)).value = 40
                ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 4).value = width
                ctypes.c_long.from_address(ctypes.addressof(bmpinfo) + 8).value = -height
                ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 12).value = 1
                ctypes.c_short.from_address(ctypes.addressof(bmpinfo) + 14).value = 32
                
                buffer_size = width * height * 4
                buffer = ctypes.create_string_buffer(buffer_size)
                
                if gdi32.GetDIBits(mem_dc, hbitmap, 0, height, buffer, bmpinfo, 0):
                    img = np.frombuffer(buffer, dtype=np.uint8)
                    img = img.reshape((height, width, 4))
                    
                    result = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                else:
                    result = None
            else:
                result = None
            
            # 清理资源
            gdi32.SelectObject(mem_dc, old_bitmap)
            gdi32.DeleteObject(hbitmap)
            gdi32.DeleteDC(mem_dc)
            user32.ReleaseDC(0, hdc)
            
            return result
            
        except Exception as e:
            logger.debug(f"直接监视器捕获失败: {e}")
            return None


if __name__ == "__main__":
    # 测试DRM绕过捕获
    drm_capture = DRMBypassCapture()
    
    print("测试DRM绕过截屏...")
    frame = drm_capture.capture_with_drm_bypass()
    
    if frame is not None:
        print(f"截屏成功！图像尺寸: {frame.shape}")
        cv2.imwrite("drm_bypass_test.png", frame)
        print("测试图像已保存为 drm_bypass_test.png")
    else:
        print("所有DRM绕过方法都失败了")
